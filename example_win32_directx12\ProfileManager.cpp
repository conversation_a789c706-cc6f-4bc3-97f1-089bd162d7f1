#include "ProfileManager.h"
#include <fstream>
#include <sstream>
#include <algorithm>
#include <Windows.h>

namespace YGMain
{
    ProfileManager::ProfileManager() {
        // Set INI file path to same directory as executable
        char exePath[MAX_PATH];
        GetModuleFileNameA(NULL, exePath, MAX_PATH);
        std::string exeDir = std::string(exePath);
        size_t lastSlash = exeDir.find_last_of("\\");
        if (lastSlash != std::string::npos) {
            exeDir = exeDir.substr(0, lastSlash + 1);
        }
        iniFilePath = exeDir + "profiles.ini";
    }

    ProfileManager::~ProfileManager() {
        SaveToFile();
    }

    void ProfileManager::Initialize() {
        LoadFromFile();
        if (profiles.empty()) {
            CreateDefaultProfiles();
            SaveToFile();
        }
    }

    void ProfileManager::CreateDefaultProfiles() {
        profiles.clear();
        
        // Game Server Profile
        CaptureProfile gameProfile;
        gameProfile.name = "Game Server";
        gameProfile.ipAddresses.push_back("*************");
        gameProfile.ipAddresses.push_back("********");
        gameProfile.ports.push_back(8080);
        gameProfile.ports.push_back(9090);
        gameProfile.loginPorts.push_back(8000);
        gameProfile.encrypted = false;
        profiles.push_back(gameProfile);

        // Login Server Profile
        CaptureProfile loginProfile;
        loginProfile.name = "Login Server";
        loginProfile.ipAddresses.push_back("auth.gameserver.com");
        loginProfile.ports.push_back(443);
        loginProfile.ports.push_back(80);
        loginProfile.loginPorts.push_back(8443);
        loginProfile.encrypted = true;
        profiles.push_back(loginProfile);

        // Local Network Profile
        CaptureProfile localProfile;
        localProfile.name = "Local Network";
        localProfile.ipAddresses.push_back("***********/24");
        localProfile.ports.push_back(22);
        localProfile.ports.push_back(80);
        localProfile.ports.push_back(443);
        localProfile.loginPorts.push_back(22);
        localProfile.encrypted = false;
        profiles.push_back(localProfile);

        // Localhost Profile
        CaptureProfile localhostProfile;
        localhostProfile.name = "Localhost";
        localhostProfile.ipAddresses.push_back("127.0.0.1");
        localhostProfile.ipAddresses.push_back("localhost");
        localhostProfile.ports.push_back(8080);
        localhostProfile.ports.push_back(3000);
        localhostProfile.ports.push_back(8000);
        localhostProfile.loginPorts.push_back(22);
        localhostProfile.encrypted = false;
        profiles.push_back(localhostProfile);
    }

    void ProfileManager::LoadFromFile() {
        profiles.clear();
        
        // Read profile count
        char buffer[256];
        DWORD result = GetPrivateProfileStringA("General", "ProfileCount", "0", buffer, sizeof(buffer), iniFilePath.c_str());
        int profileCount = atoi(buffer);
        
        // Load each profile
        for (int i = 0; i < profileCount; i++) {
            CaptureProfile profile = ReadProfileFromINI(i);
            if (!profile.name.empty()) {
                profiles.push_back(profile);
            }
        }
    }

    void ProfileManager::SaveToFile() {
        // Write profile count
        std::string countStr = std::to_string(profiles.size());
        WritePrivateProfileStringA("General", "ProfileCount", countStr.c_str(), iniFilePath.c_str());
        
        // Save each profile
        for (int i = 0; i < profiles.size(); i++) {
            WriteProfileToINI(profiles[i], i);
        }
    }

    CaptureProfile ProfileManager::ReadProfileFromINI(int index) {
        CaptureProfile profile;
        char buffer[1024];
        std::string section = "Profile" + std::to_string(index);
        
        // Read name
        DWORD result = GetPrivateProfileStringA(section.c_str(), "Name", "", buffer, sizeof(buffer), iniFilePath.c_str());
        profile.name = std::string(buffer);
        
        if (profile.name.empty()) {
            return profile; // Invalid profile
        }
        
        // Read IP addresses
        result = GetPrivateProfileStringA(section.c_str(), "IPAddresses", "", buffer, sizeof(buffer), iniFilePath.c_str());
        profile.ipAddresses = StringToStringVector(std::string(buffer));
        
        // Read ports
        result = GetPrivateProfileStringA(section.c_str(), "Ports", "", buffer, sizeof(buffer), iniFilePath.c_str());
        profile.ports = StringToIntVector(std::string(buffer));
        
        // Read login ports
        result = GetPrivateProfileStringA(section.c_str(), "LoginPorts", "", buffer, sizeof(buffer), iniFilePath.c_str());
        profile.loginPorts = StringToIntVector(std::string(buffer));
        
        // Read encrypted flag
        result = GetPrivateProfileStringA(section.c_str(), "Encrypted", "0", buffer, sizeof(buffer), iniFilePath.c_str());
        profile.encrypted = (atoi(buffer) != 0);
        
        return profile;
    }

    void ProfileManager::WriteProfileToINI(const CaptureProfile& profile, int index) {
        std::string section = "Profile" + std::to_string(index);
        
        // Write name
        WritePrivateProfileStringA(section.c_str(), "Name", profile.name.c_str(), iniFilePath.c_str());
        
        // Write IP addresses
        std::string ipStr = VectorToString(profile.ipAddresses);
        WritePrivateProfileStringA(section.c_str(), "IPAddresses", ipStr.c_str(), iniFilePath.c_str());
        
        // Write ports
        std::string portStr = VectorToString(profile.ports);
        WritePrivateProfileStringA(section.c_str(), "Ports", portStr.c_str(), iniFilePath.c_str());
        
        // Write login ports
        std::string loginPortStr = VectorToString(profile.loginPorts);
        WritePrivateProfileStringA(section.c_str(), "LoginPorts", loginPortStr.c_str(), iniFilePath.c_str());
        
        // Write encrypted flag
        std::string encryptedStr = profile.encrypted ? "1" : "0";
        WritePrivateProfileStringA(section.c_str(), "Encrypted", encryptedStr.c_str(), iniFilePath.c_str());
    }

    std::string ProfileManager::VectorToString(const std::vector<std::string>& vec) {
        std::string result;
        for (size_t i = 0; i < vec.size(); i++) {
            if (i > 0) result += "|";
            result += vec[i];
        }
        return result;
    }

    std::string ProfileManager::VectorToString(const std::vector<int>& vec) {
        std::string result;
        for (size_t i = 0; i < vec.size(); i++) {
            if (i > 0) result += "|";
            result += std::to_string(vec[i]);
        }
        return result;
    }

    std::vector<std::string> ProfileManager::StringToStringVector(const std::string& str) {
        std::vector<std::string> result;
        if (str.empty()) return result;
        
        std::stringstream ss(str);
        std::string item;
        while (std::getline(ss, item, '|')) {
            if (!item.empty()) {
                result.push_back(item);
            }
        }
        return result;
    }

    std::vector<int> ProfileManager::StringToIntVector(const std::string& str) {
        std::vector<int> result;
        if (str.empty()) return result;
        
        std::stringstream ss(str);
        std::string item;
        while (std::getline(ss, item, '|')) {
            if (!item.empty()) {
                int value = atoi(item.c_str());
                if (value > 0) {
                    result.push_back(value);
                }
            }
        }
        return result;
    }

    void ProfileManager::AddProfile(const CaptureProfile& profile) {
        profiles.push_back(profile);
        SaveToFile();
    }

    void ProfileManager::RemoveProfile(int index) {
        if (index >= 0 && index < profiles.size()) {
            profiles.erase(profiles.begin() + index);
            SaveToFile();
        }
    }

    CaptureProfile* ProfileManager::GetProfile(int index) {
        if (index >= 0 && index < profiles.size()) {
            return &profiles[index];
        }
        return nullptr;
    }

    int ProfileManager::GetProfileCount() const {
        return (int)profiles.size();
    }

    std::vector<CaptureProfile>& ProfileManager::GetProfiles() {
        return profiles;
    }

    void ProfileManager::RenderProfileManager(bool& showProfileManager) {
        if (!showProfileManager) return;

        ImGui::SetNextWindowSize(ImVec2(800, 600), ImGuiCond_FirstUseEver);
        if (ImGui::Begin("Profile Manager", &showProfileManager)) {

            // Profile list
            ImGui::Text("Existing Profiles:");
            ImGui::Separator();

            static int selectedForEdit = -1;
            static char newProfileName[256] = "";
            static char newIpAddress[256] = "";
            static char newPort[64] = "";
            static char newLoginPort[64] = "";
            static bool newEncrypted = false;

            // Left panel - Profile list
            ImGui::BeginChild("ProfileList", ImVec2(250, 0), true);
            for (int i = 0; i < profiles.size(); i++) {
                bool isSelected = (selectedForEdit == i);
                if (ImGui::Selectable(profiles[i].name.c_str(), isSelected)) {
                    selectedForEdit = i;
                    // Load profile data into edit fields
                    if (i < profiles.size()) {
                        strcpy_s(newProfileName, profiles[i].name.c_str());
                        newEncrypted = profiles[i].encrypted;
                    }
                }
                if (isSelected) ImGui::SetItemDefaultFocus();
            }
            ImGui::EndChild();

            ImGui::SameLine();

            // Right panel - Profile details
            ImGui::BeginGroup();
            ImGui::Text("Profile Details:");
            ImGui::Separator();

            // Profile name
            ImGui::Text("Profile Name:");
            ImGui::SetNextItemWidth(300);
            ImGui::InputText("##ProfileName", newProfileName, sizeof(newProfileName));

            ImGui::Spacing();

            // Current profile details (if selected)
            if (selectedForEdit >= 0 && selectedForEdit < profiles.size()) {
                CaptureProfile& profile = profiles[selectedForEdit];

                ImGui::Text("IP Addresses:");
                ImGui::BeginChild("IPList", ImVec2(0, 100), true);
                for (int i = 0; i < profile.ipAddresses.size(); i++) {
                    ImGui::Text("%d. %s", i + 1, profile.ipAddresses[i].c_str());
                    ImGui::SameLine();
                    if (ImGui::SmallButton(("X##ip" + std::to_string(i)).c_str())) {
                        profile.ipAddresses.erase(profile.ipAddresses.begin() + i);
                        SaveToFile(); // Auto-save changes
                        i--;
                    }
                }
                ImGui::EndChild();

                // Add new IP
                ImGui::SetNextItemWidth(200);
                ImGui::InputText("##NewIP", newIpAddress, sizeof(newIpAddress));
                ImGui::SameLine();
                if (ImGui::Button("Add IP") && strlen(newIpAddress) > 0) {
                    profile.ipAddresses.push_back(std::string(newIpAddress));
                    SaveToFile(); // Auto-save changes
                    newIpAddress[0] = '\0';
                }

                ImGui::Spacing();

                ImGui::Text("Ports:");
                ImGui::BeginChild("PortList", ImVec2(0, 100), true);
                for (int i = 0; i < profile.ports.size(); i++) {
                    ImGui::Text("%d. %d", i + 1, profile.ports[i]);
                    ImGui::SameLine();
                    if (ImGui::SmallButton(("X##port" + std::to_string(i)).c_str())) {
                        profile.ports.erase(profile.ports.begin() + i);
                        SaveToFile(); // Auto-save changes
                        i--;
                    }
                }
                ImGui::EndChild();

                // Add new Port
                ImGui::SetNextItemWidth(100);
                ImGui::InputText("##NewPort", newPort, sizeof(newPort));
                ImGui::SameLine();
                if (ImGui::Button("Add Port") && strlen(newPort) > 0) {
                    int port = atoi(newPort);
                    if (port > 0 && port <= 65535) {
                        profile.ports.push_back(port);
                        SaveToFile(); // Auto-save changes
                        newPort[0] = '\0';
                    }
                }

                ImGui::Spacing();

                ImGui::Text("Login Ports:");
                ImGui::BeginChild("LoginPortList", ImVec2(0, 100), true);
                for (int i = 0; i < profile.loginPorts.size(); i++) {
                    ImGui::Text("%d. %d", i + 1, profile.loginPorts[i]);
                    ImGui::SameLine();
                    if (ImGui::SmallButton(("X##lport" + std::to_string(i)).c_str())) {
                        profile.loginPorts.erase(profile.loginPorts.begin() + i);
                        SaveToFile(); // Auto-save changes
                        i--;
                    }
                }
                ImGui::EndChild();

                // Add new Login Port
                ImGui::SetNextItemWidth(100);
                ImGui::InputText("##NewLoginPort", newLoginPort, sizeof(newLoginPort));
                ImGui::SameLine();
                if (ImGui::Button("Add Login Port") && strlen(newLoginPort) > 0) {
                    int port = atoi(newLoginPort);
                    if (port > 0 && port <= 65535) {
                        profile.loginPorts.push_back(port);
                        SaveToFile(); // Auto-save changes
                        newLoginPort[0] = '\0';
                    }
                }

                ImGui::Spacing();

                // Encrypted checkbox
                if (ImGui::Checkbox("Encrypted", &profile.encrypted)) {
                    SaveToFile(); // Auto-save changes
                }

                ImGui::Spacing();
                ImGui::Separator();

                // Update profile name
                if (ImGui::Button("Update Name") && strlen(newProfileName) > 0) {
                    profile.name = std::string(newProfileName);
                    SaveToFile(); // Auto-save changes
                }

                ImGui::SameLine();

                // Delete profile
                if (ImGui::Button("Delete Profile")) {
                    RemoveProfile(selectedForEdit);
                    selectedForEdit = -1;
                    newProfileName[0] = '\0';
                }
            }

            ImGui::Spacing();
            ImGui::Separator();

            // Create new profile
            if (ImGui::Button("Create New Profile") && strlen(newProfileName) > 0) {
                CaptureProfile newProfile;
                newProfile.name = std::string(newProfileName);
                newProfile.encrypted = newEncrypted;
                AddProfile(newProfile); // Auto-saves
                newProfileName[0] = '\0';
                newEncrypted = false;
            }

            ImGui::EndGroup();
        }
        ImGui::End();
    }
}