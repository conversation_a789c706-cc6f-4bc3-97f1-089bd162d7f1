#include "Application.h"
#include "imgui.h"
#include <iostream>

namespace YGMain
{
    // Static application instance
    static PacketAnalyzerApp app;

    void PacketAnalyzerApp::Initialize() {
        // Initialize profile manager
        profileManager.Initialize();
        selectedProfileIndex = 0; // Select first profile by default

        // Initialize packet capture
        packetCapture.Initialize();
        networkInterfaces = packetCapture.GetNetworkInterfaces();
    }

    CaptureProfile* PacketAnalyzerApp::GetSelectedProfile() {
        return profileManager.GetProfile(selectedProfileIndex);
    }

    void PacketAnalyzerApp::StartRealCapture() {
        CaptureProfile* profile = GetSelectedProfile();
        if (!profile) {
            return;
        }

        int captureInterfaceIndex = selectedInterfaceIndex;

        // Check if profile contains localhost/127.0.0.1 - if so, auto-select localhost interface
        bool hasLocalhost = false;
        for (const std::string& ip : profile->ipAddresses) {
            if (ip == "127.0.0.1" || ip == "localhost") {
                hasLocalhost = true;
                break;
            }
        }

        if (hasLocalhost) {
            int localhostIndex = packetCapture.FindLocalhostInterface();
            if (localhostIndex >= 0) {
                captureInterfaceIndex = localhostIndex;
                std::cout << "Auto-selected localhost interface for capture" << std::endl;
            }
        }

        // Validate interface index
        if (captureInterfaceIndex < 0 || captureInterfaceIndex >= networkInterfaces.size()) {
            std::cout << "Invalid capture interface index" << std::endl;
            return;
        }

        // Build capture filter based on profile
        std::string filter = BuildCaptureFilter(*profile);

        // Start capturing on selected interface with filter
        if (packetCapture.StartCapture(captureInterfaceIndex, filter)) {
            isCapturing = true;
        }
    }

    void PacketAnalyzerApp::StopRealCapture() {
        packetCapture.StopCapture();
        isCapturing = false;
    }

    std::string PacketAnalyzerApp::BuildCaptureFilter(const CaptureProfile& profile) {
    std::string filter = "";
    
    // Build IP address filters - bắt cả gửi tới và nhận từ server
    if (!profile.ipAddresses.empty()) {
        std::string ipFilter = "";
        for (size_t i = 0; i < profile.ipAddresses.size(); i++) {
            if (i > 0) ipFilter += " or ";
            
            const std::string& ip = profile.ipAddresses[i];
            if (ip.find('/') != std::string::npos) {
                // CIDR notation - ***********/24
                ipFilter += "net " + ip;
            } else {
                // Single IP - 127.0.0.1, *************
                ipFilter += "host " + ip;
            }
        }
        
        if (!ipFilter.empty()) {
            filter += "(" + ipFilter + ")";
        }
    }
    
    // Build port filters - tất cả ports (game ports + login ports)
    std::vector<int> allPorts;
    allPorts.insert(allPorts.end(), profile.ports.begin(), profile.ports.end());
    allPorts.insert(allPorts.end(), profile.loginPorts.begin(), profile.loginPorts.end());
    
    if (!allPorts.empty()) {
        std::string portFilter = "";
        for (size_t i = 0; i < allPorts.size(); i++) {
            if (i > 0) portFilter += " or ";
            // Bắt cả src port và dst port = port number
            portFilter += "port " + std::to_string(allPorts[i]);
        }
        
        if (!filter.empty()) {
            filter += " and ";
        }
        filter += "(" + portFilter + ")";
    }
    
    // Chỉ bắt TCP traffic
    if (filter.empty()) {
        filter = "tcp";
    } else {
        filter += " and tcp";
    }
    
    return filter;
}

    void PacketAnalyzerApp::UpdatePacketsFromCapture() {
        // Get captured packets and convert to display format
        auto capturedPackets = packetCapture.GetCapturedPackets();

        // Update packets only if new packets were captured
        if (capturedPackets.size() != packets.size()) {
            packets.clear();
            packetStrings.clear();

            for (const auto& capturedPacket : capturedPackets) {
                PacketData packet;
                packet.id = capturedPacket.id;

                // Store timestamp string
                packetStrings.push_back(capturedPacket.timestamp);
                packet.time = packetStrings.back().c_str();

                // Store source IP
                packetStrings.push_back(capturedPacket.sourceIP);
                packet.source = packetStrings.back().c_str();

                // Store destination IP
                packetStrings.push_back(capturedPacket.destIP);
                packet.destination = packetStrings.back().c_str();

                // Store protocol
                packetStrings.push_back(capturedPacket.protocol);
                packet.protocol = packetStrings.back().c_str();

                packet.length = capturedPacket.length;

                // Store info
                packetStrings.push_back(capturedPacket.info);
                packet.info = packetStrings.back().c_str();

                // Store hex data
                packet.hexLength = (int)capturedPacket.rawData.size();
                if (packet.hexLength > 0) {
                    packet.hexData = const_cast<unsigned char*>(capturedPacket.rawData.data());
                } else {
                    packet.hexData = nullptr;
                }

                packets.push_back(packet);
            }
        }
    }

    void PacketAnalyzerApp::RenderMenuBar(bool& done) {
        if (ImGui::BeginMenuBar()) {
            if (ImGui::BeginMenu("File")) {
                if (ImGui::MenuItem("Open Capture File", "Ctrl+O")) {}
                if (ImGui::MenuItem("Save Capture", "Ctrl+S")) {}
                ImGui::Separator();
                if (ImGui::MenuItem("Exit", "Alt+F4")) { done = true; }
                ImGui::EndMenu();
            }
            if (ImGui::BeginMenu("Capture")) {
                if (ImGui::MenuItem("Interfaces...")) {}
                if (ImGui::MenuItem("Options...")) {}
                ImGui::EndMenu();
            }
            if (ImGui::BeginMenu("Analyze")) {
                if (ImGui::MenuItem("Display Filters...")) {}
                if (ImGui::MenuItem("Follow TCP Stream")) {}
                ImGui::EndMenu();
            }
            if (ImGui::BeginMenu("Help")) {
                if (ImGui::MenuItem("About")) {}
                ImGui::EndMenu();
            }
            ImGui::EndMenuBar();
        }
    }

    void PacketAnalyzerApp::RenderControlPanel() {
        ImGui::Spacing();

        // Profile selection row
        ImGui::Text("Profile:");
        ImGui::SameLine();
        ImGui::SetNextItemWidth(200);
        std::vector<CaptureProfile>& profiles = profileManager.GetProfiles();
        if (ImGui::BeginCombo("##ProfileSelect", selectedProfileIndex >= 0 && selectedProfileIndex < profiles.size() ? profiles[selectedProfileIndex].name.c_str() : "Select Profile")) {
            for (int i = 0; i < profiles.size(); i++) {
                bool isSelected = (selectedProfileIndex == i);
                if (ImGui::Selectable(profiles[i].name.c_str(), isSelected)) {
                    selectedProfileIndex = i;
                }
                if (isSelected) {
                    ImGui::SetItemDefaultFocus();
                }
            }
            ImGui::EndCombo();
        }

        ImGui::SameLine();
        if (ImGui::Button("Manage Profiles")) {
            showProfileManager = true;
        }

        ImGui::SameLine();
        if (ImGui::Button("Select Interface")) {
            showInterfaceSelector = true;
        }

        // Show selected interface
        if (selectedInterfaceIndex >= 0 && selectedInterfaceIndex < networkInterfaces.size()) {
            ImGui::SameLine();
            ImGui::Text("| Interface: %s", networkInterfaces[selectedInterfaceIndex].friendlyName.c_str());
        }

        // Show auto-select suggestion for localhost
        if (CaptureProfile* profile = GetSelectedProfile()) {
            bool hasLocalhost = false;
            for (const std::string& ip : profile->ipAddresses) {
                if (ip == "127.0.0.1" || ip == "localhost") {
                    hasLocalhost = true;
                    break;
                }
            }

            if (hasLocalhost) {
                ImGui::SameLine();
                if (ImGui::SmallButton("Auto-select Localhost")) {
                    int localhostIndex = packetCapture.FindLocalhostInterface();
                    if (localhostIndex >= 0) {
                        selectedInterfaceIndex = localhostIndex;
                    }
                }
            }
        }

        ImGui::SameLine();

        // Show selected profile info
        if (CaptureProfile* profile = GetSelectedProfile()) {
            ImGui::SameLine();
            ImGui::Text("| IPs: %d | Ports: %d | Login Ports: %d | %s",
                (int)profile->ipAddresses.size(),
                (int)profile->ports.size(),
                (int)profile->loginPorts.size(),
                profile->encrypted ? "Encrypted" : "Plain");
        }

        ImGui::Spacing();

        // Control buttons row
        // Start/Stop capture button - only enable if profile and interface are selected
        bool hasProfile = GetSelectedProfile() != nullptr;
        bool hasInterface = selectedInterfaceIndex >= 0 && selectedInterfaceIndex < networkInterfaces.size();
        bool canCapture = hasProfile && hasInterface;

        if (!canCapture) ImGui::BeginDisabled();

        if (isCapturing) {
            if (ImGui::Button("Stop Capture"))
                StopRealCapture();
        } else {
            if (ImGui::Button("Start Capture")) {
                if (canCapture) {
                    StartRealCapture();
                }
            }
        }

        if (!canCapture) ImGui::EndDisabled();

        ImGui::SameLine();
        if (ImGui::Button("Clear")) {
            packets.clear();
            packetCapture.ClearCapturedPackets();
            selectedPacket = -1;
        }

        ImGui::SameLine();
        if (ImGui::Button("Save")) {}

        ImGui::SameLine();
        if (ImGui::Button("Load")) {}

        ImGui::SameLine();
        ImGui::Text("Status: %s", isCapturing ? "Capturing..." : "Stopped");

        ImGui::SameLine();
        ImGui::Text("  |  Packets: %d", (int)packets.size());

        ImGui::Separator();
    }

    void PacketAnalyzerApp::RenderInterfaceSelector() {
        if (!showInterfaceSelector) return;

        ImGui::SetNextWindowSize(ImVec2(900, 600), ImGuiCond_FirstUseEver);
        if (ImGui::Begin("Select Network Interface", &showInterfaceSelector)) {
            ImGui::Text("Available Network Interfaces:");
            ImGui::Separator();

            // Interface list with proper height calculation
            float tableHeight = ImGui::GetContentRegionAvail().y - 120; // Reserve space for buttons and info
            if (ImGui::BeginTable("InterfaceTable", 4, ImGuiTableFlags_Borders | ImGuiTableFlags_RowBg | ImGuiTableFlags_ScrollY | ImGuiTableFlags_Resizable, ImVec2(0, tableHeight))) {
                ImGui::TableSetupColumn("Select", ImGuiTableColumnFlags_WidthFixed, 60.0f);
                ImGui::TableSetupColumn("Name", ImGuiTableColumnFlags_WidthFixed, 200.0f);
                ImGui::TableSetupColumn("Description", ImGuiTableColumnFlags_WidthStretch);
                ImGui::TableSetupColumn("Status", ImGuiTableColumnFlags_WidthFixed, 100.0f);
                ImGui::TableHeadersRow();

                // Debug: Show interface count
                if (networkInterfaces.empty()) {
                    ImGui::TableNextRow();
                    ImGui::TableNextColumn();
                    ImGui::TableNextColumn();
                    ImGui::Text("No interfaces found. Click 'Refresh List' to scan again.");
                    ImGui::TableNextColumn();
                    ImGui::TableNextColumn();
                }

                for (int i = 0; i < networkInterfaces.size(); i++) {
                    const NetworkInterface& iface = networkInterfaces[i];

                    ImGui::TableNextRow();

                    // Select radio button
                    ImGui::TableNextColumn();
                    if (ImGui::RadioButton(("##interface" + std::to_string(i)).c_str(), selectedInterfaceIndex == i)) {
                        selectedInterfaceIndex = i;
                    }

                    // Interface name
                    ImGui::TableNextColumn();
                    ImGui::Text("%s", iface.friendlyName.c_str());

                    // Description
                    ImGui::TableNextColumn();
                    ImGui::Text("%s", iface.description.c_str());

                    // Status
                    ImGui::TableNextColumn();
                    if (iface.isLoopback) {
                        ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "Loopback");
                    } else if (iface.isUp) {
                        ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "Up");
                    } else {
                        ImGui::TextColored(ImVec4(1.0f, 0.0f, 0.0f, 1.0f), "Down");
                    }

                    // Show IP addresses if interface is selected
                    if (selectedInterfaceIndex == i && !iface.addresses.empty()) {
                        ImGui::TableNextRow();
                        ImGui::TableNextColumn(); // Skip select column
                        ImGui::TableNextColumn();
                        ImGui::TableNextColumn();
                        ImGui::Text("IP Addresses:");
                        for (const auto& addr : iface.addresses) {
                            ImGui::Text("  %s", addr.c_str());
                        }
                        ImGui::TableNextColumn(); // Skip status column
                    }
                }
                ImGui::EndTable();
            }

            ImGui::Spacing();

            // Selected interface info
            if (selectedInterfaceIndex >= 0 && selectedInterfaceIndex < networkInterfaces.size()) {
                ImGui::Text("Selected Interface: %s", networkInterfaces[selectedInterfaceIndex].friendlyName.c_str());
            } else {
                ImGui::Text("No interface selected");
            }

            ImGui::Spacing();
            ImGui::Separator();

            // Buttons
            if (ImGui::Button("Refresh List")) {
                networkInterfaces = packetCapture.GetNetworkInterfaces();
                if (selectedInterfaceIndex >= networkInterfaces.size()) {
                    selectedInterfaceIndex = -1;
                }
            }
            ImGui::SameLine();
            ImGui::Text("Found %d interfaces", (int)networkInterfaces.size());

            ImGui::SameLine();

            bool hasSelection = selectedInterfaceIndex >= 0 && selectedInterfaceIndex < networkInterfaces.size();
            if (!hasSelection) ImGui::BeginDisabled();

            if (ImGui::Button("Select Interface")) {
                showInterfaceSelector = false;
            }

            if (!hasSelection) ImGui::EndDisabled();

            ImGui::SameLine();
            if (ImGui::Button("Cancel")) {
                showInterfaceSelector = false;
            }
        }
        ImGui::End();
    }

    void PacketAnalyzerApp::RenderPacketList() {
        // Packet list table
        if (ImGui::BeginTable("PacketTable", 7, ImGuiTableFlags_Borders | ImGuiTableFlags_RowBg | ImGuiTableFlags_ScrollY | ImGuiTableFlags_Resizable)) {
            ImGui::TableSetupColumn("No.", ImGuiTableColumnFlags_WidthFixed, 50.0f);
            ImGui::TableSetupColumn("Time", ImGuiTableColumnFlags_WidthFixed, 100.0f);
            ImGui::TableSetupColumn("Source", ImGuiTableColumnFlags_WidthFixed, 120.0f);
            ImGui::TableSetupColumn("Destination", ImGuiTableColumnFlags_WidthFixed, 120.0f);
            ImGui::TableSetupColumn("Protocol", ImGuiTableColumnFlags_WidthFixed, 80.0f);
            ImGui::TableSetupColumn("Length", ImGuiTableColumnFlags_WidthFixed, 60.0f);
            ImGui::TableSetupColumn("Info", ImGuiTableColumnFlags_WidthStretch);
            ImGui::TableHeadersRow();

            for (int i = 0; i < packets.size(); i++) {
                ImGui::TableNextRow();

                // Highlight selected row
                if (selectedPacket == i)
                    ImGui::TableSetBgColor(ImGuiTableBgTarget_RowBg0, ImGui::GetColorU32(ImVec4(0.3f, 0.5f, 0.8f, 0.4f)));

                ImGui::TableNextColumn();
                if (ImGui::Selectable(std::to_string(packets[i].id).c_str(), selectedPacket == i, ImGuiSelectableFlags_SpanAllColumns))
                    selectedPacket = i;

                ImGui::TableNextColumn();
                ImGui::Text("%s", packets[i].time);

                ImGui::TableNextColumn();
                ImGui::Text("%s", packets[i].source);

                ImGui::TableNextColumn();
                ImGui::Text("%s", packets[i].destination);

                ImGui::TableNextColumn();
                ImGui::Text("%s", packets[i].protocol);

                ImGui::TableNextColumn();
                ImGui::Text("%d", packets[i].length);

                ImGui::TableNextColumn();
                ImGui::Text("%s", packets[i].info);
            }
            ImGui::EndTable();
        }
    }

    void PacketAnalyzerApp::RenderPacketDetails() {
        ImGui::Text("Packet Details");
        ImGui::Separator();

        if (selectedPacket >= 0 && selectedPacket < packets.size()) {
            const PacketData& packet = packets[selectedPacket];

            // Packet information section
            ImGui::Text("Packet #%d", packet.id);
            ImGui::Text("Time: %s", packet.time);
            ImGui::Text("Source: %s", packet.source);
            ImGui::Text("Destination: %s", packet.destination);
            ImGui::Text("Protocol: %s", packet.protocol);
            ImGui::Text("Length: %d bytes", packet.length);
            ImGui::Text("Info: %s", packet.info);

            ImGui::Separator();
            ImGui::Text("Hex Dump");
            ImGui::Separator();

            // Hex dump display
            ImGui::BeginChild("HexDump", ImVec2(0, 0), true, ImGuiWindowFlags_AlwaysVerticalScrollbar);

            if (packet.hexData && packet.hexLength > 0) {
                // Display real hex data from captured packet
                const int bytesPerLine = 16;
                for (int i = 0; i < packet.hexLength; i += bytesPerLine) {
                    // Address
                    ImGui::Text("%04x  ", i);
                    ImGui::SameLine();

                    // Hex bytes
                    std::string hexLine;
                    std::string asciiLine;
                    for (int j = 0; j < bytesPerLine; j++) {
                        if (i + j < packet.hexLength) {
                            char hex[4];
                            sprintf_s(hex, "%02x ", packet.hexData[i + j]);
                            hexLine += hex;

                            // ASCII representation
                            char c = packet.hexData[i + j];
                            asciiLine += (c >= 32 && c <= 126) ? c : '.';
                        } else {
                            hexLine += "   ";
                            asciiLine += " ";
                        }

                        if (j == 7) hexLine += " "; // Extra space at middle
                    }

                    ImGui::Text("%-48s  %s", hexLine.c_str(), asciiLine.c_str());
                }
            } else {
                ImGui::Text("No packet data available");
            }

            ImGui::EndChild();
        } else {
            ImGui::Text("Select a packet to view details");
        }
    }


    void PacketAnalyzerApp::Render() {
        // Update packets from capture if we're actively capturing
        if (isCapturing) {
            UpdatePacketsFromCapture();
        }

        // Render profile manager dialog
        profileManager.RenderProfileManager(showProfileManager);

        // Render interface selector dialog
        RenderInterfaceSelector();

        // Create two columns layout
        ImGui::Columns(2, "MainSplit", true);

        // Left column - Packet list
        ImGui::Text("Packet List");
        ImGui::Separator();
        RenderPacketList();

        // Right column - Packet details and hex dump
        ImGui::NextColumn();
        RenderPacketDetails();
    }

    void RenderMainUI()
    {
        static bool initialized = false;
        static bool done = false;

        if (!initialized) {
            app.Initialize();
            initialized = true;
        }

        // Get the main viewport and render UI directly to it
        ImGuiViewport* viewport = ImGui::GetMainViewport();
        ImGui::SetNextWindowPos(viewport->Pos);
        ImGui::SetNextWindowSize(viewport->Size);
        ImGui::SetNextWindowViewport(viewport->ID);

        // Window flags to make it seamlessly embedded
        ImGuiWindowFlags window_flags = ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoCollapse | ImGuiWindowFlags_NoResize | ImGuiWindowFlags_NoMove;
        window_flags |= ImGuiWindowFlags_NoBringToFrontOnFocus | ImGuiWindowFlags_NoNavFocus | ImGuiWindowFlags_MenuBar;

        ImGui::PushStyleVar(ImGuiStyleVar_WindowRounding, 0.0f);
        ImGui::PushStyleVar(ImGuiStyleVar_WindowBorderSize, 0.0f);
        ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(0.0f, 0.0f));

        ImGui::Begin("YG Packet Analyzer", nullptr, window_flags);
        ImGui::PopStyleVar(3);

        // Render application UI
        app.RenderMenuBar(done);
        app.RenderControlPanel();
        app.Render();

        ImGui::End();
    }
}
