﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="imgui">
      <UniqueIdentifier>{fb3d294f-51ec-478e-a627-25831c80fefd}</UniqueIdentifier>
    </Filter>
    <Filter Include="sources">
      <UniqueIdentifier>{4f33ddea-9910-456d-b868-4267eb3c2b19}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="imconfig.h" />
    <ClInclude Include="imgui.h" />
    <ClInclude Include="imgui_internal.h" />
    <ClInclude Include="backends\imgui_impl_dx12.h" />
    <ClInclude Include="backends\imgui_impl_win32.h" />
    <ClInclude Include="Application.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="main.cpp">
      <Filter>sources</Filter>
    </ClCompile>
    <ClCompile Include="imgui.cpp" />
    <ClCompile Include="imgui_demo.cpp" />
    <ClCompile Include="imgui_draw.cpp" />
    <ClCompile Include="imgui_tables.cpp" />
    <ClCompile Include="imgui_widgets.cpp" />
    <ClCompile Include="backends\imgui_impl_dx12.cpp" />
    <ClCompile Include="backends\imgui_impl_win32.cpp" />
    <ClCompile Include="Application.cpp" />
  </ItemGroup>
  <ItemGroup>
    <None Include="misc\debuggers\imgui.natstepfilter" />
    <None Include="README.txt" />
  </ItemGroup>
  <ItemGroup>
    <Natvis Include="misc\debuggers\imgui.natvis" />
  </ItemGroup>
</Project>