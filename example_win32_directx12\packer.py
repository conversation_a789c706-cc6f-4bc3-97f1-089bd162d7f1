from tkinter import END
import time
from datetime import datetime
import sqlite3
import struct
import tkinter as tk
from tkinter import ttk
from tkinter.scrolledtext import ScrolledText
from scapy.all import sniff, IP, TCP, Raw
import threading
from tkinter import StringVar, But<PERSON>, messagebox
import json
from decompress import decompress_packet
from encrypt import decrypt_data
from utils.map import show_map_image
from utils.map_decode import save_map_data, save_shop_data
from utils.util_fn import manage_login_packet, manage_packet

# Global variable to control the sniffing thread
sniff_thread = None
sniffing = False
stop_sniffing = threading.Event()
fld_map = 101
table_name = 'local'

with open('data.json', 'r') as file:
    data = json.load(file)

# Initialize SQLite database and create table if not exists


def initialize_db():
    conn = sqlite3.connect("packets.db")
    cursor = conn.cursor()
    cursor.execute(f'''
        CREATE TABLE IF NOT EXISTS {table_name} (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            timestamp TEXT,
            src_ip TEXT,
            src_port INTEGER,
            dst_ip TEXT,
            dst_port INTEGER,
            request_type INTEGER,
            direction TEXT,
            raw_data_hex TEXT,
            raw_data_bytes BLOB,
            buffer BLOB,
            wordid INTEGER
        )
    ''')
    conn.commit()
    conn.close()


initialize_db()


def initialize_server_db():
    conn = sqlite3.connect('server.db')
    cursor = conn.cursor()
    cursor.execute(f'''
        CREATE TABLE IF NOT EXISTS server (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            ip TEXT NOT NULL,
            target_port INTEGER NOT NULL,
            login_port INTEGER NOT NULL,
            table_name TEXT NOT NULL,
            name TEXT NOT NULL,
            is_local INTEGER DEFAULT 0,
            is_encrypted INTEGER DEFAULT 0
        )
    ''')
    conn.commit()
    conn.close()


initialize_server_db()


def clear_database():
    conn = sqlite3.connect("packets.db")
    cursor = conn.cursor()
    cursor.execute(f"DELETE FROM {table_name}")
    conn.commit()
    conn.close()
    packets_tree.delete(*packets_tree.get_children())
    type_combobox['values'] = ("All",)
    selected_type.set("All")
    print("Database cleared and GUI updated.")


def populate_packets_table():
    packets_tree.delete(*packets_tree.get_children())
    conn = sqlite3.connect("packets.db")
    cursor = conn.cursor()
    cursor.execute(
        f"SELECT id, timestamp, dst_ip, dst_port, request_type, direction FROM {table_name} ORDER BY id ASC")
    rows = cursor.fetchall()
    conn.close()
    for row in rows:
        packets_tree.insert("", 0, values=row)


def filter_by_type(selected_type):
    selected_type_str = str(selected_type).strip()
    filtered_data = {}
    for k, v in data.items():
        key_type = k.split(",")[0].strip()
        if key_type == selected_type_str:
            filtered_data[k] = v
    options = [f"{k}: {v['name']}" for k, v in filtered_data.items()]
    return options


def refresh_packet_details():
    selected_item = packets_tree.selection()
    if selected_item:
        show_packet_details(None)


def show_packet_details(event):
    selected_item = packets_tree.selection()[0]
    packet_id = packets_tree.item(selected_item, "values")[0]
    conn = sqlite3.connect("packets.db")
    cursor = conn.cursor()
    cursor.execute(f"SELECT raw_data_hex, raw_data_bytes FROM {
                   table_name} WHERE id=?", (packet_id,))
    packet = cursor.fetchone()
    conn.close()
    hex_data_text.delete("1.0", tk.END)
    if packet:
        hex_data, byte_data = packet
        if is_decompress_var.get() and len(byte_data) > 32:
            byte_data = decompress_packet(byte_data)
        if hex_data and byte_data:
            formatted_hex = format_hex_data(byte_data.hex())
            request_type, wordid = manage_packet(byte_data)
            world_label.config(text=f"WorldID : {wordid}")
            typehex_label.config(
                text=f"Type : {request_type} - {filter_by_type(request_type)}")
            for line, address, hex_str, byte_str in formatted_hex:
                start_index = hex_data_text.index(tk.END)
                hex_data_text.insert(tk.END, f"{address}  ")
                hex_data_text.insert(tk.END, f"{hex_str:<47}", "hex")
                hex_data_text.insert(tk.END, f"  {byte_str}\n")
                end_index = hex_data_text.index(tk.END)
                hex_data_text.tag_add("address", start_index, f"{
                                      start_index} + {len(address)}c")
                hex_data_text.tag_add(
                    "byte", f"{end_index} - {len(byte_str) + 4}c", end_index)
                hex_data_text.tag_config(
                    "address", foreground="grey", selectbackground="white", selectforeground="grey")
                hex_data_text.tag_config(
                    "hex", foreground="black", selectbackground="yellow", selectforeground="black")
                hex_data_text.tag_config(
                    "byte", foreground="grey", selectbackground="white", selectforeground="grey")
            root.clipboard_clear()
            root.clipboard_append(byte_data.hex())
            root.update()
            sync_scroll('moveto', 0)


def format_hex_data(hex_data):
    formatted_lines = []
    for i in range(0, len(hex_data), 32):
        hex_chunk = hex_data[i:i+32]
        hex_str = " ".join(hex_chunk[j:j+2]
                           for j in range(0, len(hex_chunk), 2))
        address = f"{i:08X}"
        byte_data = bytearray.fromhex(hex_chunk)
        byte_str = "".join(chr(b) if 32 <= b <=
                           126 else '.' for b in byte_data)
        formatted_lines.append((f"{address}  {hex_str:<47}  {
                               byte_str}", address, hex_str, byte_str))
    return formatted_lines


def on_hex_selection(event):
    try:
        if not hex_data_text.tag_ranges("sel"):
            return
        start_index = hex_data_text.index(tk.SEL_FIRST)
        end_index = hex_data_text.index(tk.SEL_LAST)
        start_tag = hex_data_text.tag_names(start_index)
        end_tag = hex_data_text.tag_names(end_index)
        if "hex" in start_tag and "hex" in end_tag:
            selected_text = hex_data_text.get(
                tk.SEL_FIRST, tk.SEL_LAST).replace(" ", "").replace("\n", "")
            selected_text = "".join(
                c for c in selected_text if c in "0123456789abcdefABCDEF")
            update_data_interpretation(selected_text)
        else:
            hex_data_text.tag_remove(tk.SEL, "1.0", tk.END)
    except tk.TclError:
        pass


def update_data_interpretation(hex_data):
    data_interpretation_text.config(state=tk.NORMAL)
    data_interpretation_text.delete("1.0", tk.END)
    try:
        byte_data = bytes.fromhex(hex_data)
        if len(byte_data) > 0:
            data_interpretation_text.insert(tk.END, f"Hex: {hex_data}\n")
            for size, fmt in [
                (1, 'int8'), (1, 'uint8'),
                (2, 'int16'), (2, 'uint16'),
                (3, 'int24'), (3, 'uint24'),
                (4, 'int32'), (4, 'uint32'),
                (8, 'int64'), (8, 'uint64'),
                (4, 'float32'), (8, 'float64')
            ]:
                if len(byte_data) >= size:
                    val = int.from_bytes(
                        byte_data[:size], byteorder='little', signed=('int' in fmt))
                    data_interpretation_text.insert(tk.END, f"{fmt}: {val}\n")
            data_interpretation_text.insert(
                tk.END, f"ANSI Char: {byte_data.decode('ascii', errors='replace')}\n")
            data_interpretation_text.insert(
                tk.END, f"Wide Char: {byte_data.decode('utf-16le', errors='replace')}\n")
    except Exception as e:
        data_interpretation_text.insert(
            tk.END, f"Error interpreting data: {e}\n")
    data_interpretation_text.config(state=tk.DISABLED)


def stop_capture():
    global sniffing, stop_sniffing
    if sniffing:
        stop_sniffing.set()
        sniffing = False
        status_label.config(text="Status: Stopped")


def sync_scroll(*args):
    hex_data_text.yview(*args)


root = tk.Tk()
root.title("PPKCAP")

server_frame = tk.Frame(root)
server_frame.pack(side=tk.TOP, fill=tk.X)

server_combobox = ttk.Combobox(server_frame, state="readonly")

control_frame = tk.Frame(root)
control_frame.pack(side=tk.TOP, fill=tk.X)

character_frame = tk.Frame(root)
character_frame.pack(side=tk.BOTTOM, fill=tk.X)

start_button = tk.Button(control_frame, text="Start Capture",
                         command=lambda: start_capture(target_ip))
start_button.pack(side=tk.LEFT, padx=5, pady=5)
stop_button = tk.Button(
    control_frame, text="Stop Capture", command=stop_capture)
stop_button.pack(side=tk.LEFT, padx=5, pady=5)

status_label = tk.Label(control_frame, text="Status: Stopped")
status_label.pack(side=tk.LEFT, padx=5, pady=5)

fld_name = ""
fld_level = ""

character_name_label = tk.Label(character_frame, text=f"Name : {fld_name}")
character_name_label.pack(side=tk.LEFT, padx=5, pady=5)
character_level_label = tk.Label(character_frame, text=f"Level : {fld_level}")
character_level_label.pack(side=tk.LEFT, padx=5, pady=5)
current_map_label = tk.Label(character_frame, text=f"Map : {fld_map}")
current_map_label.pack(side=tk.LEFT, padx=5, pady=5)

clear_button = tk.Button(
    control_frame, text="Clear Database", command=clear_database)
clear_button.pack(side=tk.LEFT, padx=5, pady=5)

left_frame = tk.Frame(root)
left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

right_frame = tk.Frame(root)
right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

selected_type = StringVar()
selected_type.set("All")

type_label = tk.Label(control_frame, text="Filter by Type:")
type_label.pack(side=tk.LEFT, padx=5, pady=5)

type_combobox = ttk.Combobox(
    control_frame, textvariable=selected_type, state="readonly")
type_combobox.pack(side=tk.LEFT, padx=5, pady=5)
type_combobox['values'] = ("All",)

type_input_entry = tk.Entry(control_frame)
type_input_entry.pack(side=tk.LEFT, padx=5, pady=5)


def on_filter_button_click():
    global input_types
    input_text = type_input_entry.get()
    selected_type.set("All")
    if input_text:
        input_types = [t.strip() for t in input_text.split(',')]
        filter_packets_by_types(input_types)


filter_button = tk.Button(control_frame, text="Filter",
                          command=on_filter_button_click)
filter_button.pack(side=tk.LEFT, padx=5, pady=5)


request_input_entry = tk.Entry(control_frame)
request_input_entry.pack(side=tk.LEFT, padx=5, pady=5)

""" def on_ignore_button_click():
    global packet_ignore
    input_text2 = request_input_entry.get()
    if input_text2:
        packet_ignore = [t.strip() for t in input_text2.split(',')]


filter2_button = tk.Button(control_frame, text="Ignore Packet",
                          command=on_ignore_button_click)
filter2_button.pack(side=tk.LEFT, padx=5, pady=5) """

quit_button = tk.Button(character_frame, text="Quit", command=root.destroy)
quit_button.pack(side=tk.RIGHT, padx=5, pady=5)


def load_servers():
    conn = sqlite3.connect("server.db")
    cursor = conn.cursor()
    cursor.execute("SELECT id, name FROM server")
    servers = cursor.fetchall()
    conn.close()
    return servers


def update_server_combobox():
    servers = load_servers()
    server_combobox['values'] = [
        f"{server[0]}: {server[1]}" for server in servers]


def on_server_selected(event):
    selected_server = server_combobox.get()
    if selected_server:
        server_id = int(selected_server.split(":")[0])
        conn = sqlite3.connect("server.db")
        cursor = conn.cursor()
        cursor.execute(
            "SELECT ip, target_port, login_port,table_name FROM server WHERE id=?", (server_id,))
        server_data = cursor.fetchone()
        conn.close()
        if server_data:
            global target_ip, target_port, login_port, table_name
            target_ip, target_port, login_port, table_name = server_data
            initialize_db()
            populate_packets_table()
            initial_type_combobox()


def open_server_window(is_edit=False):
    global table_name
    server_window = tk.Toplevel(root)
    server_window.title("Add/Edit Server")

    ip_label = tk.Label(server_window, text="IP:")
    ip_label.grid(row=0, column=0, padx=5, pady=5)
    ip_entry = tk.Entry(server_window)
    ip_entry.grid(row=0, column=1, padx=5, pady=5)

    target_port_label = tk.Label(server_window, text="Target Port:")
    target_port_label.grid(row=1, column=0, padx=5, pady=5)
    target_port_entry = tk.Entry(server_window)
    target_port_entry.grid(row=1, column=1, padx=5, pady=5)

    login_port_label = tk.Label(server_window, text="Login Port:")
    login_port_label.grid(row=2, column=0, padx=5, pady=5)
    login_port_entry = tk.Entry(server_window)
    login_port_entry.grid(row=2, column=1, padx=5, pady=5)

    table_name_label = tk.Label(server_window, text="Table_Name:")
    table_name_label.grid(row=3, column=0, padx=5, pady=5)
    table_name_entry = tk.Entry(server_window)
    table_name_entry.grid(row=3, column=1, padx=5, pady=5)

    name_label = tk.Label(server_window, text="Name:")
    name_label.grid(row=4, column=0, padx=5, pady=5)
    name_entry = tk.Entry(server_window)
    name_entry.grid(row=4, column=1, padx=5, pady=5)

    is_local_var = tk.IntVar()
    is_local_checkbox = tk.Checkbutton(
        server_window, text="Capture Local Traffic", variable=is_local_var)
    is_local_checkbox.grid(row=5, columnspan=2, padx=5, pady=5)

    is_encrypted_var = tk.IntVar()
    is_encrypted_checkbox = tk.Checkbutton(
        server_window, text="Encrypted", variable=is_encrypted_var)
    is_encrypted_checkbox.grid(row=6, columnspan=2, padx=5, pady=5)

    if is_edit:
        selected_server = server_combobox.get()
        if selected_server:
            server_id = int(selected_server.split(":")[0])
            conn = sqlite3.connect("server.db")
            cursor = conn.cursor()
            cursor.execute(
                'SELECT ip, target_port, login_port,table_name, name, is_local, is_encrypted FROM server WHERE id=?', (server_id,))
            server_data = cursor.fetchone()
            conn.close()

            if server_data:
                ip_entry.insert(0, server_data[0])
                target_port_entry.insert(0, server_data[1])
                login_port_entry.insert(0, server_data[2])
                table_name_entry.insert(0, server_data[3])
                name_entry.insert(0, server_data[4])
                is_local_var.set(server_data[5])
                is_encrypted_var.set(server_data[6])

    def save_server():
        ip = ip_entry.get()
        target_port = target_port_entry.get()
        login_port = login_port_entry.get()
        name = name_entry.get()
        table_name = table_name_entry.get()
        is_local = is_local_var.get()
        is_encrypted = is_encrypted_var.get()

        conn = sqlite3.connect("server.db")
        cursor = conn.cursor()

        if is_edit:
            selected_server = server_combobox.get()
            if selected_server:
                server_id = int(selected_server.split(":")[0])
                cursor.execute('''
                    UPDATE server SET ip=?, target_port=?, login_port=?,table_name=?, name=?, is_local=?, is_encrypted=? WHERE id=?
                ''', (ip, target_port, login_port, table_name, name, is_local, is_encrypted, server_id))
        else:
            cursor.execute('''
                INSERT INTO server (ip, target_port, login_port, table_name, name, is_local, is_encrypted)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (ip, target_port, login_port, table_name, name, is_local, is_encrypted))

        conn.commit()
        conn.close()
        server_window.destroy()
        update_server_combobox()
        initialize_db()

    save_button = tk.Button(server_window, text="Save", command=save_server)
    save_button.grid(row=7, columnspan=2, padx=5, pady=5)


def delete_server():
    selected_server = server_combobox.get()
    if not selected_server:
        messagebox.showwarning("Delete Server", "No server selected!")
        return

    server_id = int(selected_server.split(":")[0])

    conn = sqlite3.connect("server.db")
    cursor = conn.cursor()
    cursor.execute("DELETE FROM server WHERE id=?", (server_id,))
    conn.commit()
    conn.close()

    update_server_combobox()


update_server_combobox()
server_combobox.pack(side=tk.LEFT, padx=5, pady=5)
server_combobox.bind("<<ComboboxSelected>>", on_server_selected)

add_button = tk.Button(server_frame, text="Add Server",
                       command=lambda: open_server_window(False))
add_button.pack(side=tk.LEFT, padx=5, pady=5)

edit_button = tk.Button(server_frame, text="Edit Server",
                        command=lambda: open_server_window(True))
edit_button.pack(side=tk.LEFT, padx=5, pady=5)

delete_button = tk.Button(
    server_frame, text="Delete Server", command=delete_server)
delete_button.pack(side=tk.LEFT, padx=5, pady=5)


def initial_type_combobox():
    conn = sqlite3.connect("packets.db")
    cursor = conn.cursor()
    cursor.execute(f"SELECT DISTINCT request_type FROM {table_name}")
    packets = cursor.fetchall()
    conn.close()

    current_types = type_combobox['values']
    current_types_int = [int(t) for t in current_types if t != "All"]

    if packets:
        new_types = sorted([col[0] for col in packets if col[0]
                           is not None and col[0] not in current_types_int])
        type_combobox["values"] = tuple(
            ["All"] + sorted(current_types_int + new_types))


initial_type_combobox()


def update_type_combobox(new_type):
    if new_type is not None:
        current_types = type_combobox['values']
        current_types_int = [int(t) for t in current_types if t != "All"]
        if new_type not in current_types_int:
            type_combobox['values'] = tuple(
                ["All"] + sorted(current_types_int + [new_type]))


def on_type_selected(event):
    filter_packets_by_type(selected_type.get())


type_combobox.bind("<<ComboboxSelected>>", on_type_selected)

columns = ("ID", "Time", "Destination", "Port", "Type", "Side")
packets_tree = ttk.Treeview(left_frame, columns=columns, show="headings")

packets_tree.tag_configure('Send', background='red')
packets_tree.tag_configure('Recv', background='green')

for col in columns:
    packets_tree.heading(col, text=col)
    packets_tree.column(col, width=50 if col in [
                        "S.Port", "D.Port", "ID", "Type", "Side"] else 120 if col == "Time" else 80)

packets_tree.pack(fill=tk.BOTH, expand=True, side=tk.LEFT)

scroll_y = tk.Scrollbar(left_frame, orient=tk.VERTICAL,
                        command=packets_tree.yview)
scroll_y.pack(side=tk.RIGHT, fill=tk.Y)
packets_tree.configure(yscroll=scroll_y.set)

scroll_x = tk.Scrollbar(left_frame, orient=tk.HORIZONTAL,
                        command=packets_tree.xview)
scroll_x.pack(side=tk.BOTTOM, fill=tk.X)
packets_tree.configure(xscroll=scroll_x.set)

packets_tree.bind("<<TreeviewSelect>>", show_packet_details)

right_frame_top = tk.Frame(right_frame, width=200)
right_frame_top.pack(side=tk.TOP, fill=tk.BOTH, expand=False)
right_frame_bottom = tk.Frame(right_frame, width=200)
right_frame_bottom.pack(side=tk.BOTTOM, fill=tk.BOTH, expand=False)
right_frame_status = tk.Frame(right_frame, width=200)
right_frame_bottom.pack(side=tk.BOTTOM, fill=tk.BOTH, expand=False)

is_decompress_var = tk.IntVar()
check_decompress = tk.Checkbutton(
    right_frame_top, text="Decompress", variable=is_decompress_var, command=refresh_packet_details)
check_decompress.pack(side=tk.LEFT, padx=5, pady=5)
world_label = tk.Label(right_frame_top, text="World ID: ")
world_label.pack(side=tk.LEFT, padx=5, pady=5)
typehex_label = tk.Label(right_frame_top, text="Type ID: ")
typehex_label.pack(side=tk.LEFT, padx=5, pady=5)

data_frame = tk.Frame(right_frame_bottom)
data_frame.pack(side=tk.TOP, fill=tk.BOTH, expand=True)

hex_data_text = ScrolledText(data_frame, wrap=tk.WORD, width=100)
hex_data_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
hex_data_text.bind("<<Selection>>", on_hex_selection)

data_interpretation_text = ScrolledText(
    right_frame_bottom, wrap=tk.WORD, width=50, state=tk.DISABLED)
data_interpretation_text.pack(side=tk.TOP, fill=tk.BOTH, expand=False)


def sync_scroll(*args):
    hex_data_text.yview(*args)


hex_data_text.config(yscrollcommand=sync_scroll)
scroll_y = tk.Scrollbar(data_frame, orient=tk.VERTICAL, command=sync_scroll)
scroll_y.pack(side=tk.RIGHT, fill=tk.Y)
hex_data_text.config(yscrollcommand=scroll_y.set)

populate_packets_table()


def filter_packets_by_types(types):
    packets_tree.delete(*packets_tree.get_children())
    conn = sqlite3.connect("packets.db")
    cursor = conn.cursor()
    placeholders = ','.join('?' for _ in types)
    query = f"SELECT id, timestamp, dst_ip, dst_port, request_type, direction FROM {
        table_name} WHERE request_type IN ({placeholders}) ORDER BY timestamp DESC"
    cursor.execute(query, types)
    rows = cursor.fetchall()
    conn.close()
    for row in rows:
        packets_tree.insert("", tk.END, values=row)


def filter_packets_by_type(packet_type):
    packets_tree.delete(*packets_tree.get_children())
    conn = sqlite3.connect("packets.db")
    cursor = conn.cursor()
    if packet_type == "All":
        cursor.execute(f"SELECT id, timestamp, dst_ip, dst_port, request_type, direction FROM {
                       table_name} ORDER BY timestamp DESC")
    else:
        cursor.execute(f"SELECT id, timestamp, dst_ip, dst_port, request_type, direction FROM {
                       table_name} WHERE request_type=? ORDER BY timestamp DESC", (packet_type,))
    rows = cursor.fetchall()
    conn.close()
    for row in rows:
        packets_tree.insert("", tk.END, values=row)


def insert_buffer(timestamp, ip_layer, tcp_layer, request_type, direction, wordid, sub_packet, buffer):
    conn = sqlite3.connect("packets.db")
    cursor = conn.cursor()
    cursor.execute(
        f"""
        INSERT INTO {table_name} (timestamp, src_ip, src_port, dst_ip, dst_port, request_type, direction, raw_data_hex, raw_data_bytes, buffer, wordid)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    """,
        (
            timestamp,
            ip_layer.src,
            tcp_layer.sport,
            ip_layer.dst,
            tcp_layer.dport,
            request_type,
            direction,
            sub_packet.hex(),
            sub_packet,
            buffer,
            wordid,
        ),
    )
    conn.commit()
    last_id = cursor.lastrowid
    conn.close()
    return last_id


buffer = b""


# Define a global list to collect packets
packet_buffer = []
batch_size = 100  # Define the batch size
update_interval = 0.5  # Define update interval in seconds


def update_treeview_periodically():
    global packet_buffer
    if packet_buffer:
        for row in packet_buffer:
            packets_tree.insert("", 0, values=row)
        packet_buffer = []
    root.after(int(update_interval * 1000), update_treeview_periodically)


# Start the periodic update
root.after(int(update_interval * 1000), update_treeview_periodically)


def packet_callback(packet):
    global buffer, fld_map, target_port, login_port, packet_buffer, packet_ignore
    if packet.haslayer(IP) and packet.haslayer(TCP) and packet.haslayer(Raw):
        ip_layer = packet.getlayer(IP)
        tcp_layer = packet.getlayer(TCP)
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        if tcp_layer.dport == login_port or ip_layer.sport == login_port:
            print("Login - " + str(tcp_layer.dport) + " " + str(ip_layer.sport))
            direction = ">>>" if ip_layer.dport == login_port else "<"
            raw_data = packet[Raw].load
            sub_packet = bytes(raw_data)
            request_type, wordid = manage_login_packet(sub_packet)
            update_type_combobox(request_type)
            last_id = insert_buffer(
                timestamp, ip_layer, tcp_layer, request_type, direction, wordid, sub_packet, buffer)
            tag = 'send' if direction == "Send" else 'recv'
            if selected_type.get() in ("All", request_type):
                packet_buffer.append((
                    last_id,
                    timestamp,
                    ip_layer.dst,
                    tcp_layer.dport,
                    request_type,
                    direction,
                ))
            return

        direction = ">>>" if ip_layer.dport == target_port else "<"
        raw_data = packet[Raw].load
        data_bytes = bytes(raw_data)

        # In ra console các packet không bắt đầu bằng AA55 hoặc không kết thúc bằng 55AA
        if not data_bytes.startswith(b'\xAA\x55') or not data_bytes.endswith(b'\x55\xAA'):
            print(f"Packet không chuẩn - IP:{ip_layer.src}:{
                  tcp_layer.sport} -> {ip_layer.dst}:{tcp_layer.dport}")
            print(f"  - Data length: {len(data_bytes)} bytes")
            print(f"  - Hex: {data_bytes.hex()}")
            if len(data_bytes) > 0:
                print(f"  - Bắt đầu: {data_bytes[:2].hex()}")
                print(f"  - Kết thúc: {data_bytes[-2:].hex()}")
            print("----------")

        buffer += data_bytes

        while True:
            end_index = buffer.find(b'\x55\xAA')
            if end_index == -1:
                break
            start_index = buffer.find(b'\xAA\x55')
            if start_index == -1 or start_index > end_index:
                break
            sub_packet = buffer[start_index:end_index+2]
            buffer = buffer[end_index+2:]
            sub_packet = decrypt_data(sub_packet) if len(
                sub_packet) >= 16 and is_encrypted else sub_packet
            request_type, wordid = manage_packet(sub_packet)

            # Check against packet_filter and skip if request_type is in the filter
            input_text2 = request_input_entry.get()
            packet_ignore = [t.strip() for t in input_text2.split(',')]
            if str(request_type) in packet_ignore:
                continue

            if request_type is None:
                buffer = b""
                return
            if request_type == 103:
                save_map_data(sub_packet, 101)
            if request_type == 642:
                save_shop_data(sub_packet)
            before_decompress = sub_packet
            update_type_combobox(request_type)
            last_id = insert_buffer(timestamp, ip_layer, tcp_layer, request_type,
                                    direction, wordid, sub_packet, before_decompress)
            tag = 'send' if direction == "Send" else 'recv'
            input_text = type_input_entry.get()
            filter_types = [t.strip()
                            for t in input_text.split(',')] if input_text else []
            if selected_type.get() in ("All", request_type):
                if len(filter_types) > 0 and request_type not in filter_types:
                    return
                packet_buffer.append((
                    last_id,
                    timestamp,
                    ip_layer.dst,
                    tcp_layer.dport,
                    request_type,
                    direction,
                ))

        if len(buffer) > 0 and buffer[-2:] != b'\x55\xAA':
            incomplete_start = buffer.find(b'\xAA\x55')
            if incomplete_start != -1:
                buffer = buffer[incomplete_start:]
            else:
                buffer = b""


def start_capture(ip):
    global target_ip, sniffing, sniff_thread, stop_sniffing
    if not sniffing:
        target_ip = ip
        stop_sniffing.clear()
        sniff_thread = threading.Thread(target=sniff_packets)
        sniff_thread.start()
        sniffing = True
        status_label.config(text=f"Status: Capturing {target_ip}")


def sniff_packets():
    global stop_sniffing, login_port, is_encrypted, table_name
    selected_server = server_combobox.get()
    if not selected_server:
        messagebox.showerror("Error", "No server selected!")
        return
    selected_id = int(selected_server.split(":")[0])
    conn = sqlite3.connect("server.db")
    cursor = conn.cursor()
    cursor.execute(
        "SELECT ip, target_port, is_local, login_port, is_encrypted, table_name FROM server WHERE id=?", (selected_id,))
    row = cursor.fetchone()
    conn.close()
    # show_map_image(101);
    if row:
        target_ip, target_port, is_local, login_port, is_encrypted, table_name = row

        bpf_filter = f"host {target_ip} and port {
            target_port} or port {login_port}"

        def stop_filter(packet):
            return stop_sniffing.is_set()
        # NordLynx Ethernet
        adapter = 'Ethernet'
        # adapter = 'NordLynx'
        # adapter = 'TinyGPN'
        if is_local:
            loopback_iface = '\\Device\\NPF_Loopback'
            bpf_filter = f"host {target_ip} and port {
                target_port} or port {login_port}"
        else:
            loopback_iface = adapter
            bpf_filter = f"host {target_ip}"
        sniff(filter=bpf_filter, iface=loopback_iface,
              prn=packet_callback, store=0, stop_filter=stop_filter)
    else:
        messagebox.showerror("Error", "Server data missing or incorrect!")
    # bpf_filter = f"host {target_ip} and tcp"


# Start the GUI main loop
root.geometry("1150x700")
root.mainloop()
