@REM Build for Visual Studio compiler. Run your copy of vcvars32.bat or vcvar<PERSON>l.bat to setup command-line compiler.
@set OUT_DIR=Debug
@set OUT_EXE=example_win32_directx12
@set INCLUDES=/I..\.. /I..\..\backends /I "%WindowsSdkDir%Include\um" /I "%WindowsSdkDir%Include\shared" /I "E:\YulgangDev\PacketCapture\npcap-sdk-1.15\Include"
@set SOURCES=main.cpp Application.cpp ProfileManager.cpp PacketCapture.cpp ..\..\backends\imgui_impl_dx12.cpp ..\..\backends\imgui_impl_win32.cpp ..\..\imgui*.cpp
@set LIBS=d3d12.lib d3dcompiler.lib dxgi.lib wpcap.lib Packet.lib ws2_32.lib
mkdir Debug
cl /nologo /Zi /MD /utf-8 %INCLUDES% /D UNICODE /D _UNICODE %SOURCES% /Fe%OUT_DIR%/%OUT_EXE%.exe /Fo%OUT_DIR%/ /link %LIBS% /LIBPATH:"E:\YulgangDev\PacketCapture\npcap-sdk-1.15\Lib\x64"
