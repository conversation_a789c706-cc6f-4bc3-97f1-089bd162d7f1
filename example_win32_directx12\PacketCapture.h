#pragma once

#ifndef _WINSOCK2API_
#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#endif
#define _WINSOCK_DEPRECATED_NO_WARNINGS
#ifndef _WIN32_WINNT
#define _WIN32_WINNT 0x0600
#endif
#include <winsock2.h>
#include <ws2tcpip.h>
#include <windows.h>
#endif

#define HAVE_REMOTE
#define WPCAP
#define HAVE_NPCAP_API

// Force NpCap usage instead of WinPcap
#include <pcap.h>

#include <vector>
#include <string>
#include <memory>
#include <thread>
#include <atomic>
#include <mutex>
#include <functional>

namespace YGMain {

    struct NetworkInterface {
        std::string name;
        std::string description;
        std::string friendlyName;
        bool isLoopback;
        bool isUp;
        std::vector<std::string> addresses;
    };

    struct CapturedPacket {
        int id;
        std::string timestamp;
        std::string sourceIP;
        std::string destIP;
        std::string protocol;
        int sourcePort;
        int destPort;
        int length;
        std::string info;
        std::vector<unsigned char> rawData;
        std::vector<unsigned char> payloadData;  // Only the actual data payload
        bool hasPayload;                         // Flag to indicate if packet has data
    };

    class PacketCapture {
    public:
        PacketCapture();
        ~PacketCapture();

        bool Initialize();
        void Cleanup();

        std::vector<NetworkInterface> GetNetworkInterfaces();
        std::vector<NetworkInterface> GetWindowsNetworkInterfaces(); // Fallback method
        std::vector<NetworkInterface> GetWindowsRegistryInterfaces(); // Registry-based method
        std::vector<NetworkInterface> GetPcapNetworkInterfaces(); // Direct pcap method
        int FindLocalhostInterface(); // Find localhost/loopback interface
        bool StartCapture(int interfaceIndex, const std::string& filter = "");
        void StopCapture();
        bool IsCapturing() const { return isCapturing.load(); }

        std::vector<CapturedPacket> GetCapturedPackets();
        void ClearCapturedPackets();

        void SetPacketCallback(std::function<void(const CapturedPacket&)> callback);

    private:
        pcap_t* captureHandle;
        pcap_t* writeHandle; // Additional write handle
        std::vector<NetworkInterface> interfaces;
        std::vector<CapturedPacket> capturedPackets;
        std::atomic<bool> isCapturing;
        std::atomic<bool> shouldStop;
        std::thread captureThread;
        std::mutex packetMutex;
        std::function<void(const CapturedPacket&)> packetCallback;
        int packetIdCounter;

        static void PacketHandler(u_char* user, const struct pcap_pkthdr* header, const u_char* packet);
        void ProcessPacket(const struct pcap_pkthdr* header, const u_char* packet);
        void CaptureThreadFunction();

        std::string ParseEthernetFrame(const u_char* packet, int length, CapturedPacket& capturedPacket);
        std::string ParseIPPacket(const u_char* packet, int length, CapturedPacket& capturedPacket);
        std::string ParseTCPPacket(const u_char* packet, int length, CapturedPacket& capturedPacket);
        std::string ParseUDPPacket(const u_char* packet, int length, CapturedPacket& capturedPacket);

        std::string IPAddressToString(uint32_t ip);
        std::string GetCurrentTimestamp();
    };
}
