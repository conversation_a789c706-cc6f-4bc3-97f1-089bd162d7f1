# Visual Studio files
.vs/
*.sln.docstates
*.suo
*.user
*.aps
*.pch
*.vspscc
*_i.c
*_p.c
*.ncb
*.tlb
*.tlh
*.bak
*.cache
*.ilk
*.log
*.sbr
*.sdf
*.opensdf
*.unsuccessfulbuild
ipch/

# Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
build/
bld/
[Bb]in/
[Oo]bj/

# Visual Studio Code
.vscode/

# CLion
.idea/
cmake-build-*/

# Executables
*.exe
*.out
*.app
*.dll
*.so
*.dylib

# Object files
*.o
*.obj
*.ko
*.elf

# Precompiled Headers
*.gch
*.pch

# Libraries
*.lib
*.a
*.la
*.lo

# Shared objects (inc. Windows DLLs)
*.dll
*.so
*.so.*
*.dylib

# Static libraries
*.lai
*.la
*.a
*.lib

# Debug files
*.dSYM/
*.su
*.idb
*.pdb

# CMake
CMakeCache.txt
CMakeFiles/
CMakeScripts/
Testing/
Makefile
cmake_install.cmake
install_manifest.txt
compile_commands.json
CTestTestfile.cmake
_deps

# Ninja
.ninja_deps
.ninja_log

# Qt
*.pro.user
*.pro.user.*
*.qbs.user
*.qbs.user.*
*.moc
moc_*.cpp
moc_*.h
qrc_*.cpp
ui_*.h
*.qmlc
*.jsc
Makefile*
*build-*

# DirectX/ImGui specific
*.pdb
*.idb
*.tlog
*.lastbuildstate
*.unsuccessfulbuild
*.exp
*.manifest
*.res
*.rc

# User-specific files
*.rsuser
*.suo
*.user
*.userosscache
*.sln.docstates

# User-specific folders
[Aa]uto_Generated_Files/

# Windows image file caches
Thumbs.db
ehthumbs.db

# Folder config file
Desktop.ini

# Recycle Bin used on file shares
$RECYCLE.BIN/

# Windows Installer files
*.cab
*.msi
*.msm
*.msp

# Windows shortcuts
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride

# Icon must end with two \r
Icon

# Thumbnails
._*

# Files that might appear in the root of a volume
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Directories potentially created on remote AFP share
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~

# temporary files which can be created if a process still has a handle open of a deleted file
.fuse_hidden*

# KDE directory preferences
.directory

# Linux trash folder which might appear on any partition or disk
.Trash-*

# .nfs files are created when an open file is removed but is still being accessed
.nfs*

# Claude Code files
.claude/
*.claude

# Documentation and text files (as requested)
*.md
*.txt
README*
LICENSE*
CHANGELOG*
NOTES*

# Logs
*.log
log/
logs/

# Temporary files
*.tmp
*.temp
*.swp
*.swo
*~

# Package files
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# IDE and editor files
*.sublime-*
*.atom
*.brackets.json

# JetBrains IDEs
.idea/
*.iws
*.iml
*.ipr

# CodeBlocks
*.layout
*.depend

# Dev-C++
*.dev

# MinGW
*.a

# Backup files
*.bak
*.backup
*.old
*.orig

# Test results
TestResults/

# Coverage results
*.coverage
*.coveragexml

# Profiling results
*.psess
*.vsp
*.vspx
*.sap

# Others
*.cachefile
*.VC.db
*.VC.VC.opendb

# ImGui specific
imgui.ini