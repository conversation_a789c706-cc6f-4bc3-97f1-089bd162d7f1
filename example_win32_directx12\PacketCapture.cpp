﻿#include "PacketCapture.h"
#include <iostream>
#include <sstream>
#include <iomanip>
#include <chrono>
#include <iphlpapi.h>

#pragma comment(lib, "wpcap.lib")
#pragma comment(lib, "Packet.lib")
#pragma comment(lib, "ws2_32.lib")
#pragma comment(lib, "iphlpapi.lib")

namespace YGMain {

    PacketCapture::PacketCapture()
        : captureHandle(nullptr), isCapturing(false), shouldStop(false), packetIdCounter(0) {
    }

    PacketCapture::~PacketCapture() {
        Cleanup();
    }

    bool PacketCapture::Initialize() {
        // Initialize Winsock for NpCap
        WSADATA wsaData;
        int result = WSAStartup(MAKEWORD(2, 2), &wsaData);
        if (result != 0) {
            std::cerr << "WSAStartup failed: " << result << std::endl;
            return false;
        }

        // Check if NpCap service is running
        std::cout << "Checking NpCap service status..." << std::endl;
        SC_HANDLE scm = OpenSCManager(NULL, NULL, SC_MANAGER_CONNECT);
        if (scm) {
            SC_HANDLE service = OpenService(scm, L"npcap", SERVICE_QUERY_STATUS);
            if (service) {
                SERVICE_STATUS status;
                if (QueryServiceStatus(service, &status)) {
                    if (status.dwCurrentState == SERVICE_RUNNING) {
                        std::cout << "NpCap service is running" << std::endl;
                    } else {
                        std::cout << "NpCap service is not running (state: " << status.dwCurrentState << ")" << std::endl;
                    }
                } else {
                    std::cout << "Failed to query NpCap service status" << std::endl;
                }
                CloseServiceHandle(service);
            } else {
                std::cout << "NpCap service not found" << std::endl;
            }
            CloseServiceHandle(scm);
        } else {
            std::cout << "Failed to open Service Control Manager" << std::endl;
        }

        return true;
    }

    void PacketCapture::Cleanup() {
        StopCapture();
        WSACleanup();
    }

    std::vector<NetworkInterface> PacketCapture::GetNetworkInterfaces() {
        interfaces.clear();

        char errbuf[PCAP_ERRBUF_SIZE];
        pcap_if_t* alldevs;
        pcap_if_t* device;

        std::cout << "Scanning for network interfaces..." << std::endl;

        if (pcap_findalldevs(&alldevs, errbuf) == -1) {
            std::cerr << "Error finding devices: " << errbuf << std::endl;
            return interfaces;
        }

        std::cout << "pcap_findalldevs succeeded" << std::endl;

        // Check if any devices were found
        if (alldevs == nullptr) {
            std::cout << "No devices found by pcap_findalldevs" << std::endl;
        } else {
            std::cout << "Device list is not null, enumerating..." << std::endl;
        }

        int index = 0;
        for (device = alldevs; device != nullptr; device = device->next) {
            NetworkInterface netInterface;
            netInterface.name = device->name ? device->name : "";
            netInterface.description = device->description ? device->description : "No description";

            // Extract friendly name from description
            std::string desc = netInterface.description;

            // Try to extract friendly name between quotes or parentheses
            size_t startPos = desc.find('\'');
            if (startPos != std::string::npos) {
                size_t endPos = desc.find('\'', startPos + 1);
                if (endPos != std::string::npos) {
                    netInterface.friendlyName = desc.substr(startPos + 1, endPos - startPos - 1);
                } else {
                    netInterface.friendlyName = desc.substr(startPos + 1);
                }
            } else {
                // Try parentheses
                startPos = desc.find('(');
                if (startPos != std::string::npos) {
                    size_t endPos = desc.find(')', startPos + 1);
                    if (endPos != std::string::npos) {
                        netInterface.friendlyName = desc.substr(startPos + 1, endPos - startPos - 1);
                    } else {
                        netInterface.friendlyName = desc.substr(startPos + 1);
                    }
                } else {
                    // Use the first part before any special characters
                    size_t specialPos = desc.find_first_of("{(");
                    if (specialPos != std::string::npos) {
                        netInterface.friendlyName = desc.substr(0, specialPos);
                        // Trim trailing spaces
                        while (!netInterface.friendlyName.empty() && netInterface.friendlyName.back() == ' ') {
                            netInterface.friendlyName.pop_back();
                        }
                    } else {
                        netInterface.friendlyName = netInterface.description;
                    }
                }
            }

            // If friendly name is empty, use description
            if (netInterface.friendlyName.empty()) {
                netInterface.friendlyName = netInterface.description;
            }

            netInterface.isLoopback = (device->flags & PCAP_IF_LOOPBACK) != 0;
            netInterface.isUp = (device->flags & PCAP_IF_UP) != 0;

            // Get IP addresses
            for (pcap_addr_t* addr = device->addresses; addr != nullptr; addr = addr->next) {
                if (addr->addr && addr->addr->sa_family == AF_INET) {
                    struct sockaddr_in* addr_in = (struct sockaddr_in*)addr->addr;
                    char ip_str[INET_ADDRSTRLEN];
                    inet_ntop(AF_INET, &(addr_in->sin_addr), ip_str, INET_ADDRSTRLEN);
                    netInterface.addresses.push_back(std::string(ip_str));
                }
            }

            std::cout << "Interface " << index << ": " << netInterface.friendlyName << std::endl;
            std::cout << "  Name: " << netInterface.name << std::endl;
            std::cout << "  Description: " << netInterface.description << std::endl;
            std::cout << "  Status: " << (netInterface.isUp ? "Up" : "Down") << std::endl;

            interfaces.push_back(netInterface);
            index++;
        }

        std::cout << "Total interfaces found: " << interfaces.size() << std::endl;

        pcap_freealldevs(alldevs);

        // If no interfaces found via pcap, try Windows API fallback
        if (interfaces.empty()) {
            std::cout << "No interfaces found via pcap, trying Windows API fallback..." << std::endl;
            return GetWindowsNetworkInterfaces();
        }

        return interfaces;
    }

    std::vector<NetworkInterface> PacketCapture::GetWindowsNetworkInterfaces() {
        std::vector<NetworkInterface> windowsInterfaces;

        // Get adapters info using Windows API
        ULONG ulOutBufLen = sizeof(IP_ADAPTER_INFO);
        PIP_ADAPTER_INFO pAdapterInfo = (IP_ADAPTER_INFO*)malloc(sizeof(IP_ADAPTER_INFO));
        if (pAdapterInfo == nullptr) {
            return windowsInterfaces;
        }

        // Make an initial call to GetAdaptersInfo to get
        // the necessary size into the ulOutBufLen variable
        if (GetAdaptersInfo(pAdapterInfo, &ulOutBufLen) == ERROR_BUFFER_OVERFLOW) {
            free(pAdapterInfo);
            pAdapterInfo = (IP_ADAPTER_INFO*)malloc(ulOutBufLen);
            if (pAdapterInfo == nullptr) {
                return windowsInterfaces;
            }
        }

        DWORD dwRetVal;
        if ((dwRetVal = GetAdaptersInfo(pAdapterInfo, &ulOutBufLen)) == NO_ERROR) {
            PIP_ADAPTER_INFO pAdapter = pAdapterInfo;
            int index = 0;
            while (pAdapter) {
                NetworkInterface netInterface;

                // Create name in format that might work with NpCap
                netInterface.name = "\\Device\\NPF_" + std::string(pAdapter->AdapterName);
                netInterface.description = std::string(pAdapter->Description);
                netInterface.friendlyName = std::string(pAdapter->Description);

                // Check if adapter is up
                netInterface.isUp = (pAdapter->Type != MIB_IF_TYPE_LOOPBACK);
                netInterface.isLoopback = (pAdapter->Type == MIB_IF_TYPE_LOOPBACK);

                // Get IP addresses
                PIP_ADDR_STRING pIpAddrString = &(pAdapter->IpAddressList);
                while (pIpAddrString) {
                    if (strcmp(pIpAddrString->IpAddress.String, "0.0.0.0") != 0) {
                        netInterface.addresses.push_back(std::string(pIpAddrString->IpAddress.String));
                    }
                    pIpAddrString = pIpAddrString->Next;
                }

                std::cout << "Windows Interface " << index << ": " << netInterface.friendlyName << std::endl;
                std::cout << "  Name: " << netInterface.name << std::endl;
                std::cout << "  Type: " << pAdapter->Type << std::endl;
                std::cout << "  Status: " << (netInterface.isUp ? "Up" : "Down") << std::endl;

                windowsInterfaces.push_back(netInterface);
                pAdapter = pAdapter->Next;
                index++;
            }
        } else {
            std::cout << "GetAdaptersInfo failed with error: " << dwRetVal << std::endl;
        }

        if (pAdapterInfo) {
            free(pAdapterInfo);
        }

        std::cout << "Total Windows interfaces found: " << windowsInterfaces.size() << std::endl;
        return windowsInterfaces;
    }

    int PacketCapture::FindLocalhostInterface() {
        std::vector<NetworkInterface> currentInterfaces = GetNetworkInterfaces();

        // Look for loopback interface first
        for (int i = 0; i < currentInterfaces.size(); i++) {
            if (currentInterfaces[i].isLoopback) {
                std::cout << "Found loopback interface at index " << i << ": " << currentInterfaces[i].friendlyName << std::endl;
                return i;
            }
        }

        // Look for interface with 127.0.0.1
        for (int i = 0; i < currentInterfaces.size(); i++) {
            for (const std::string& addr : currentInterfaces[i].addresses) {
                if (addr == "127.0.0.1") {
                    std::cout << "Found localhost interface at index " << i << ": " << currentInterfaces[i].friendlyName << std::endl;
                    return i;
                }
            }
        }

        std::cout << "No localhost interface found" << std::endl;
        return -1;
    }

    bool PacketCapture::StartCapture(int interfaceIndex, const std::string& filter) {
        if (isCapturing.load()) {
            return false;
        }

        // Get current interface list
        std::vector<NetworkInterface> currentInterfaces = GetNetworkInterfaces();

        if (interfaceIndex < 0 || interfaceIndex >= currentInterfaces.size()) {
            std::cerr << "Invalid interface index: " << interfaceIndex << " (available: 0-" << (currentInterfaces.size()-1) << ")" << std::endl;
            return false;
        }

        std::cout << "Starting capture on interface: " << currentInterfaces[interfaceIndex].friendlyName << std::endl;
        std::cout << "Interface name: " << currentInterfaces[interfaceIndex].name << std::endl;

       char errbuf[PCAP_ERRBUF_SIZE];
    
    // Validate input
    if (interfaceIndex < 0 || interfaceIndex >= static_cast<int>(currentInterfaces.size())) {
        std::cerr << "Invalid interface index: " << interfaceIndex << std::endl;
        return false;
    }
    
    const std::string& interfaceName = currentInterfaces[interfaceIndex].name;
    if (interfaceName.empty()) {
        std::cerr << "Interface name is empty" << std::endl;
        return false;
    }
    
    // Clear error buffer
    memset(errbuf, 0, PCAP_ERRBUF_SIZE);
    
    // Open interface
    captureHandle = pcap_open_live(
        interfaceName.c_str(),
        65536,    // snapshot length
        1,        // promiscuous mode
        1000,     // timeout (ms) - tăng timeout
        errbuf
    );
        if (captureHandle == nullptr) {
            std::cerr << "Error opening interface: " << errbuf << std::endl;
            return false;
        }

        // Set filter if provided
        if (!filter.empty()) {
            struct bpf_program compiled_filter;
            if (pcap_compile(captureHandle, &compiled_filter, filter.c_str(), 0, PCAP_NETMASK_UNKNOWN) == -1) {
                std::cerr << "Error compiling filter: " << pcap_geterr(captureHandle) << std::endl;
                pcap_close(captureHandle);
                captureHandle = nullptr;
                return false;
            }

            if (pcap_setfilter(captureHandle, &compiled_filter) == -1) {
                std::cerr << "Error setting filter: " << pcap_geterr(captureHandle) << std::endl;
                pcap_freecode(&compiled_filter);
                pcap_close(captureHandle);
                captureHandle = nullptr;
                return false;
            }

            pcap_freecode(&compiled_filter);
        }

        shouldStop.store(false);
        isCapturing.store(true);
        packetIdCounter = 0;

        // Start capture thread
        captureThread = std::thread(&PacketCapture::CaptureThreadFunction, this);

        return true;
    }

    void PacketCapture::StopCapture() {
        if (!isCapturing.load()) {
            return;
        }

        shouldStop.store(true);
        isCapturing.store(false);

        if (captureHandle) {
            pcap_breakloop(captureHandle);
        }

        if (captureThread.joinable()) {
            captureThread.join();
        }

        if (captureHandle) {
            pcap_close(captureHandle);
            captureHandle = nullptr;
        }
    }

    void PacketCapture::CaptureThreadFunction() {
        while (!shouldStop.load() && captureHandle) {
            int result = pcap_dispatch(captureHandle, 1, PacketHandler, reinterpret_cast<u_char*>(this));
            if (result == -1) {
                std::cerr << "Error in pcap_dispatch: " << pcap_geterr(captureHandle) << std::endl;
                break;
            }
            if (result == 0) {
                // Timeout, continue
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }
        }
    }

    void PacketCapture::PacketHandler(u_char* user, const struct pcap_pkthdr* header, const u_char* packet) {
        PacketCapture* capture = reinterpret_cast<PacketCapture*>(user);
        capture->ProcessPacket(header, packet);
    }

    void PacketCapture::ProcessPacket(const struct pcap_pkthdr* header, const u_char* packet) {
        CapturedPacket capturedPacket;
        capturedPacket.id = ++packetIdCounter;
        capturedPacket.timestamp = GetCurrentTimestamp();
        capturedPacket.length = header->len;
        capturedPacket.rawData.assign(packet, packet + header->caplen);

        // Parse ethernet frame
        std::string info = ParseEthernetFrame(packet, header->caplen, capturedPacket);
        capturedPacket.info = info;

        // Store packet
        {
            std::lock_guard<std::mutex> lock(packetMutex);
            capturedPackets.push_back(capturedPacket);
        }

        // Call callback if set
        if (packetCallback) {
            packetCallback(capturedPacket);
        }
    }

    std::string PacketCapture::ParseEthernetFrame(const u_char* packet, int length, CapturedPacket& capturedPacket) {
        if (length < 14) {
            capturedPacket.protocol = "Unknown";
            return "Malformed ethernet frame";
        }

        // Skip ethernet header (14 bytes) and check for IP
        uint16_t etherType = (packet[12] << 8) | packet[13];

        if (etherType == 0x0800) { // IPv4
            return ParseIPPacket(packet + 14, length - 14, capturedPacket);
        } else if (etherType == 0x0806) { // ARP
            capturedPacket.protocol = "ARP";
            return "ARP packet";
        } else {
            capturedPacket.protocol = "Other";
            return "Non-IP packet (EtherType: 0x" + std::to_string(etherType) + ")";
        }
    }

    std::string PacketCapture::ParseIPPacket(const u_char* packet, int length, CapturedPacket& capturedPacket) {
        if (length < 20) {
            capturedPacket.protocol = "IP";
            return "Malformed IP packet";
        }

        // Extract IP header information
        uint8_t headerLength = (packet[0] & 0x0F) * 4;
        uint8_t protocol = packet[9];

        // Source and destination IP addresses
        uint32_t srcIP = *reinterpret_cast<const uint32_t*>(&packet[12]);
        uint32_t destIP = *reinterpret_cast<const uint32_t*>(&packet[16]);

        capturedPacket.sourceIP = IPAddressToString(srcIP);
        capturedPacket.destIP = IPAddressToString(destIP);

        if (length < headerLength) {
            capturedPacket.protocol = "IP";
            return "Malformed IP packet (header length mismatch)";
        }

        // Parse transport layer
        const u_char* payload = packet + headerLength;
        int payloadLength = length - headerLength;

        switch (protocol) {
            case 6: // TCP
                return ParseTCPPacket(payload, payloadLength, capturedPacket);
            case 17: // UDP
                return ParseUDPPacket(payload, payloadLength, capturedPacket);
            case 1: // ICMP
                capturedPacket.protocol = "ICMP";
                capturedPacket.sourcePort = 0;
                capturedPacket.destPort = 0;
                return "ICMP packet";
            default:
                capturedPacket.protocol = "IP";
                capturedPacket.sourcePort = 0;
                capturedPacket.destPort = 0;
                return "IP packet (protocol: " + std::to_string(protocol) + ")";
        }
    }

    std::string PacketCapture::ParseTCPPacket(const u_char* packet, int length, CapturedPacket& capturedPacket) {
        if (length < 20) {
            capturedPacket.protocol = "TCP";
            return "Malformed TCP packet";
        }

        capturedPacket.protocol = "TCP";
        capturedPacket.sourcePort = ntohs(*reinterpret_cast<const uint16_t*>(&packet[0]));
        capturedPacket.destPort = ntohs(*reinterpret_cast<const uint16_t*>(&packet[2]));

        // TCP flags
        uint8_t flags = packet[13];
        std::string flagStr = "";
        if (flags & 0x01) flagStr += "FIN ";
        if (flags & 0x02) flagStr += "SYN ";
        if (flags & 0x04) flagStr += "RST ";
        if (flags & 0x08) flagStr += "PSH ";
        if (flags & 0x10) flagStr += "ACK ";
        if (flags & 0x20) flagStr += "URG ";

        return "TCP [" + flagStr + "] " + std::to_string(capturedPacket.sourcePort) +
               " -> " + std::to_string(capturedPacket.destPort);
    }

    std::string PacketCapture::ParseUDPPacket(const u_char* packet, int length, CapturedPacket& capturedPacket) {
        if (length < 8) {
            capturedPacket.protocol = "UDP";
            return "Malformed UDP packet";
        }

        capturedPacket.protocol = "UDP";
        capturedPacket.sourcePort = ntohs(*reinterpret_cast<const uint16_t*>(&packet[0]));
        capturedPacket.destPort = ntohs(*reinterpret_cast<const uint16_t*>(&packet[2]));

        return "UDP " + std::to_string(capturedPacket.sourcePort) +
               " -> " + std::to_string(capturedPacket.destPort);
    }

    std::string PacketCapture::IPAddressToString(uint32_t ip) {
        struct sockaddr_in addr;
        addr.sin_addr.S_un.S_addr = ip;
        char str[INET_ADDRSTRLEN];
        inet_ntop(AF_INET, &(addr.sin_addr), str, INET_ADDRSTRLEN);
        return std::string(str);
    }

    std::string PacketCapture::GetCurrentTimestamp() {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()) % 1000;

        std::stringstream ss;
        struct tm timeinfo;
        localtime_s(&timeinfo, &time_t);
        ss << std::put_time(&timeinfo, "%H:%M:%S");
        ss << '.' << std::setfill('0') << std::setw(3) << ms.count();
        return ss.str();
    }

    std::vector<CapturedPacket> PacketCapture::GetCapturedPackets() {
        std::lock_guard<std::mutex> lock(packetMutex);
        return capturedPackets;
    }

    void PacketCapture::ClearCapturedPackets() {
        std::lock_guard<std::mutex> lock(packetMutex);
        capturedPackets.clear();
        packetIdCounter = 0;
    }

    void PacketCapture::SetPacketCallback(std::function<void(const CapturedPacket&)> callback) {
        packetCallback = callback;
    }
}
