#pragma once

#include "imgui.h"
#include "ProfileManager.h"
#include "PacketCapture.h"
#include <vector>
#include <string>

namespace YGMain
{
    // Packet capture state (for backward compatibility)
    struct PacketData {
        int id;
        const char* time;
        const char* source;
        const char* destination;
        const char* protocol;
        int length;
        const char* info;
        unsigned char* hexData;
        int hexLength;
    };
    // Application state
    class PacketAnalyzerApp {
    public:
        std::vector<PacketData> packets;
        std::vector<std::string> packetStrings; // Persistent storage for packet display strings
        int selectedPacket = -1;
        bool isCapturing = false;

        // Profile management
        ProfileManager profileManager;
        int selectedProfileIndex = -1;
        bool showProfileManager = false;

        // Packet capture
        PacketCapture packetCapture;
        std::vector<NetworkInterface> networkInterfaces;
        bool showInterfaceSelector = false;
        int selectedInterfaceIndex = -1;

        void Initialize();
        void RenderMenuBar(bool& done);
        void RenderControlPanel();
        void RenderInterfaceSelector();
        void RenderPacketList();
        void RenderPacketDetails();
        void UpdatePacketsFromCapture();
        void Render();

        // Profile helper functions
        CaptureProfile* GetSelectedProfile();

        // Capture helper functions
        void StartRealCapture();
        void StopRealCapture();
        std::string BuildCaptureFilter(const CaptureProfile& profile);
    };

    void RenderMainUI();
}
