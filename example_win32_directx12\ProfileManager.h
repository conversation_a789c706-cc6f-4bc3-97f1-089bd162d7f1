#pragma once

#include "imgui.h"
#include <vector>
#include <string>

namespace YGMain
{
    // Profile for packet capture configuration
    struct CaptureProfile {
        std::string name;
        std::vector<std::string> ipAddresses;
        std::vector<int> ports;
        std::vector<int> loginPorts;
        bool encrypted;

        CaptureProfile() : encrypted(false) {}

        CaptureProfile(const std::string& profileName)
            : name(profileName), encrypted(false) {}
    };

    // Profile Manager class
    class ProfileManager {
    public:
        ProfileManager();
        ~ProfileManager();

        // Profile management
        void Initialize();
        void LoadFromFile();
        void SaveToFile();
        void CreateDefaultProfiles();
        
        // Profile operations
        void AddProfile(const CaptureProfile& profile);
        void RemoveProfile(int index);
        CaptureProfile* GetProfile(int index);
        int GetProfileCount() const;
        std::vector<CaptureProfile>& GetProfiles();
        
        // UI Rendering
        void RenderProfileManager(bool& showProfileManager);
        
    private:
        std::vector<CaptureProfile> profiles;
        std::string iniFilePath;
        
        // INI file helpers
        void WriteProfileToINI(const CaptureProfile& profile, int index);
        CaptureProfile ReadProfileFromINI(int index);
        int GetProfileCountFromINI();
        std::string VectorToString(const std::vector<std::string>& vec);
        std::string VectorToString(const std::vector<int>& vec);
        std::vector<std::string> StringToStringVector(const std::string& str);
        std::vector<int> StringToIntVector(const std::string& str);
    };
}